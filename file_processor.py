import csv
import os
import logging
from typing import List, Tuple, Dict, Any
from datetime import datetime
import urllib.parse

logger = logging.getLogger(__name__)

class FileProcessor:
    """Service class for handling file upload, validation, and processing."""

    def __init__(self, config=None):
        """Initialize file processor."""
        self.config = config or {}
        self.allowed_extensions = self.config.get('ALLOWED_EXTENSIONS', {'tsv', 'txt'})
        self.results_dir = self.config.get('RESULTS_FOLDER', 'results')
        self.max_file_size = self.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)
        os.makedirs(self.results_dir, exist_ok=True)
    
    def is_valid_file(self, file) -> bool:
        """
        Check if uploaded file has valid extension.
        
        Args:
            file: Flask file object
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not file or not file.filename:
            return False
        
        filename = file.filename.lower()
        return '.' in filename and filename.rsplit('.', 1)[1] in self.allowed_extensions
    
    def validate_tsv_format(self, filepath: str) -> Dict[str, Any]:
        """
        Validate TSV file format and content.
        
        Args:
            filepath (str): Path to the uploaded file
            
        Returns:
            Dict[str, Any]: Validation result with status and details
        """
        try:
            with open(filepath, 'r', encoding='utf-8', newline='') as file:
                # Try to detect if it's actually tab-separated
                sample = file.read(1024)
                file.seek(0)
                
                if '\t' not in sample:
                    return {
                        'valid': False,
                        'error': 'File does not appear to be tab-separated. Please ensure URLs are separated by tab characters.'
                    }
                
                reader = csv.reader(file, delimiter='\t')
                row_count = 0
                invalid_rows = []
                
                for row_num, row in enumerate(reader, 1):
                    row_count += 1
                    
                    # Skip empty rows
                    if not row or all(cell.strip() == '' for cell in row):
                        continue
                    
                    # Check if row has exactly 2 columns
                    if len(row) != 2:
                        invalid_rows.append(f"Row {row_num}: Expected 2 URLs, found {len(row)} columns")
                        continue
                    
                    # Validate URLs
                    url1, url2 = row[0].strip(), row[1].strip()
                    
                    if not url1 or not url2:
                        invalid_rows.append(f"Row {row_num}: Empty URL(s) found")
                        continue
                    
                    if not self._is_valid_url(url1):
                        invalid_rows.append(f"Row {row_num}: Invalid URL1 format: {url1}")
                    
                    if not self._is_valid_url(url2):
                        invalid_rows.append(f"Row {row_num}: Invalid URL2 format: {url2}")
                
                if invalid_rows:
                    error_msg = "File validation failed:\n" + "\n".join(invalid_rows[:10])
                    if len(invalid_rows) > 10:
                        error_msg += f"\n... and {len(invalid_rows) - 10} more errors"
                    
                    return {
                        'valid': False,
                        'error': error_msg
                    }
                
                if row_count == 0:
                    return {
                        'valid': False,
                        'error': 'File is empty or contains no valid data rows'
                    }
                
                return {
                    'valid': True,
                    'row_count': row_count,
                    'message': f'File validation successful. Found {row_count} URL pairs.'
                }
                
        except UnicodeDecodeError:
            return {
                'valid': False,
                'error': 'File encoding error. Please ensure the file is saved as UTF-8.'
            }
        except Exception as e:
            logger.error(f"Error validating TSV file {filepath}: {str(e)}")
            return {
                'valid': False,
                'error': f'File validation error: {str(e)}'
            }
    
    def parse_tsv_file(self, filepath: str) -> List[Tuple[str, str]]:
        """
        Parse TSV file and extract URL pairs.
        
        Args:
            filepath (str): Path to the TSV file
            
        Returns:
            List[Tuple[str, str]]: List of URL pairs
        """
        url_pairs = []
        
        try:
            with open(filepath, 'r', encoding='utf-8', newline='') as file:
                reader = csv.reader(file, delimiter='\t')
                
                for row_num, row in enumerate(reader, 1):
                    # Skip empty rows
                    if not row or all(cell.strip() == '' for cell in row):
                        continue
                    
                    if len(row) >= 2:
                        url1 = row[0].strip()
                        url2 = row[1].strip()
                        
                        if url1 and url2:
                            url_pairs.append((url1, url2))
                        else:
                            logger.warning(f"Skipping row {row_num}: Empty URL(s)")
                    else:
                        logger.warning(f"Skipping row {row_num}: Insufficient columns")
            
            logger.info(f"Parsed {len(url_pairs)} URL pairs from {filepath}")
            return url_pairs
            
        except Exception as e:
            logger.error(f"Error parsing TSV file {filepath}: {str(e)}")
            raise Exception(f"Failed to parse TSV file: {str(e)}")
    
    def generate_results_file(self, results: List[Dict[str, Any]], original_filename: str) -> str:
        """
        Generate TSV results file from processing results.
        
        Args:
            results (List[Dict[str, Any]]): Processing results
            original_filename (str): Original uploaded filename
            
        Returns:
            str: Generated results filename
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_name = os.path.splitext(original_filename)[0]
            results_filename = f"{base_name}_results_{timestamp}.tsv"
            results_filepath = os.path.join(self.results_dir, results_filename)
            
            with open(results_filepath, 'w', encoding='utf-8', newline='') as file:
                writer = csv.writer(file, delimiter='\t')
                
                # Write header
                header = [
                    'URL1', 'URL2', 'Status', 'Doc1_ID', 'Doc2_ID', 'Comparison_Count',
                    'RECID', 'CM_DOC_ID', 'REV_DOC_ID', 'VENDOR', 'DIFF_FILE_PATH',
                    'CHANGED_FEATURES', 'INPUT_FEATURES', 'CM_LINE_PN', 'REV_LINE_PN',
                    'FAILED_LINE_CM', 'FAILED_LINE_REV', 'FAILED_AT_STRUCTURED', 'Error_Message'
                ]
                writer.writerow(header)
                
                # Write results
                for result in results:
                    if result['status'] == 'success' and 'data' in result:
                        data = result['data']
                        if data['status'] == 'success' and 'comparisons' in data:
                            # Write one row per comparison
                            for comp in data['comparisons']:
                                row = [
                                    result['url1'], result['url2'], 'success',
                                    data.get('doc1_id', ''), data.get('doc2_id', ''),
                                    data.get('comparison_count', ''),
                                    comp.get('recid', ''), comp.get('cm_doc_id', ''),
                                    comp.get('rev_doc_id', ''), comp.get('vendor', ''),
                                    comp.get('diff_file_path', ''), comp.get('changed_features', ''),
                                    comp.get('input_features', ''), comp.get('cm_line_pn', ''),
                                    comp.get('rev_line_pn', ''), comp.get('failed_line_cm', ''),
                                    comp.get('failed_line_rev', ''), comp.get('failed_at_structured', ''),
                                    ''
                                ]
                                writer.writerow(row)
                        else:
                            # No comparison data found
                            row = [
                                result['url1'], result['url2'], data['status'],
                                data.get('doc1_id', ''), data.get('doc2_id', ''), '',
                                '', '', '', '', '', '', '', '', '', '', '', '',
                                data.get('message', data.get('error', ''))
                            ]
                            writer.writerow(row)
                    else:
                        # Error case
                        row = [
                            result['url1'], result['url2'], 'error',
                            '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                            result.get('error', 'Unknown error')
                        ]
                        writer.writerow(row)
            
            logger.info(f"Generated results file: {results_filepath}")
            return results_filename
            
        except Exception as e:
            logger.error(f"Error generating results file: {str(e)}")
            raise Exception(f"Failed to generate results file: {str(e)}")
    
    def _is_valid_url(self, url: str) -> bool:
        """
        Validate URL format.
        
        Args:
            url (str): URL to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            result = urllib.parse.urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
