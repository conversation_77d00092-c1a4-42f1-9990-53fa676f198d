import csv
import os
import logging
from typing import List, Tuple, Dict, Any
from datetime import datetime
import urllib.parse

logger = logging.getLogger(__name__)

class FileProcessor:
    """Service class for handling file upload, validation, and processing."""

    def __init__(self, config=None):
        """Initialize file processor."""
        self.config = config or {}
        self.allowed_extensions = self.config.get('ALLOWED_EXTENSIONS', {'tsv', 'txt'})
        self.results_dir = self.config.get('RESULTS_FOLDER', 'results')
        self.max_file_size = self.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)
        os.makedirs(self.results_dir, exist_ok=True)
    
    def is_valid_file(self, file) -> bool:
        """
        Check if uploaded file has valid extension.
        
        Args:
            file: Flask file object
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not file or not file.filename:
            return False
        
        filename = file.filename.lower()
        return '.' in filename and filename.rsplit('.', 1)[1] in self.allowed_extensions
    
    def validate_tsv_format(self, filepath: str) -> Dict[str, Any]:
        """
        Basic validation - just check if file exists and is readable.
        We'll process invalid rows in the output instead of rejecting them.

        Args:
            filepath (str): Path to the uploaded file

        Returns:
            Dict[str, Any]: Validation result with status and details
        """
        try:
            with open(filepath, 'r', encoding='utf-8', newline='') as file:
                content = file.read()

                if not content.strip():
                    return {
                        'valid': False,
                        'error': 'File is empty'
                    }

                lines = content.split('\n')
                row_count = len([line for line in lines if line.strip()])

                return {
                    'valid': True,
                    'row_count': row_count,
                    'message': f'File uploaded successfully. Found {row_count} rows.'
                }

        except UnicodeDecodeError:
            return {
                'valid': False,
                'error': 'File encoding error. Please ensure the file is saved as UTF-8.'
            }
        except Exception as e:
            logger.error(f"Error validating TSV file {filepath}: {str(e)}")
            return {
                'valid': False,
                'error': f'File validation error: {str(e)}'
            }
    
    def parse_tsv_file(self, filepath: str) -> List[Dict[str, Any]]:
        """
        Parse TSV file and extract all rows, including invalid ones.

        Args:
            filepath (str): Path to the TSV file

        Returns:
            List[Dict[str, Any]]: List of row data with validation info
        """
        rows_data = []

        try:
            with open(filepath, 'r', encoding='utf-8', newline='') as file:
                lines = file.readlines()

                for row_num, line in enumerate(lines, 1):
                    line = line.strip()

                    # Skip empty lines
                    if not line:
                        continue

                    # Split by tab
                    parts = line.split('\t')

                    row_data = {
                        'row_number': row_num,
                        'original_line': line,
                        'valid': False,
                        'url1': '',
                        'url2': '',
                        'error': ''
                    }

                    if len(parts) < 2:
                        row_data['error'] = f"Row {row_num}: Expected 2 URLs separated by tab, found {len(parts)} parts"
                    elif len(parts) > 2:
                        row_data['error'] = f"Row {row_num}: Too many columns ({len(parts)}), using first 2"
                        row_data['url1'] = parts[0].strip()
                        row_data['url2'] = parts[1].strip()
                        if row_data['url1'] and row_data['url2']:
                            row_data['valid'] = True
                    else:
                        row_data['url1'] = parts[0].strip()
                        row_data['url2'] = parts[1].strip()

                        if not row_data['url1'] or not row_data['url2']:
                            row_data['error'] = f"Row {row_num}: Empty URL(s) found"
                        elif not self._is_valid_url(row_data['url1']):
                            row_data['error'] = f"Row {row_num}: Invalid URL1 format"
                        elif not self._is_valid_url(row_data['url2']):
                            row_data['error'] = f"Row {row_num}: Invalid URL2 format"
                        else:
                            row_data['valid'] = True

                    rows_data.append(row_data)

            valid_count = len([r for r in rows_data if r['valid']])
            logger.info(f"Parsed {len(rows_data)} total rows from {filepath}, {valid_count} valid")
            return rows_data

        except Exception as e:
            logger.error(f"Error parsing TSV file {filepath}: {str(e)}")
            raise Exception(f"Failed to parse TSV file: {str(e)}")
    
    def generate_results_file(self, results: List[Dict[str, Any]], original_filename: str) -> str:
        """
        Generate TXT results file from processing results.

        Args:
            results (List[Dict[str, Any]]): Processing results
            original_filename (str): Original uploaded filename

        Returns:
            str: Generated results filename
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            base_name = os.path.splitext(original_filename)[0]
            results_filename = f"{base_name}_results_{timestamp}.txt"
            results_filepath = os.path.join(self.results_dir, results_filename)

            with open(results_filepath, 'w', encoding='utf-8', newline='') as file:
                writer = csv.writer(file, delimiter='\t')

                # Write header
                header = [
                    'Row_Number', 'URL1', 'URL2', 'Status', 'Doc1_ID', 'Doc2_ID', 'Comparison_Count',
                    'RECID', 'CM_DOC_ID', 'REV_DOC_ID', 'VENDOR', 'DIFF_FILE_PATH',
                    'CHANGED_FEATURES', 'INPUT_FEATURES', 'CM_LINE_PN', 'REV_LINE_PN',
                    'FAILED_LINE_CM', 'FAILED_LINE_REV', 'FAILED_AT_STRUCTURED', 'Error_Message'
                ]
                writer.writerow(header)

                # Write results
                for result in results:
                    row_number = result.get('row_number', '')

                    if result['status'] == 'success' and 'data' in result:
                        data = result['data']
                        if data['status'] == 'success' and 'comparisons' in data:
                            # Write one row per comparison
                            for comp in data['comparisons']:
                                row = [
                                    row_number, result['url1'], result['url2'], 'success',
                                    data.get('doc1_id', ''), data.get('doc2_id', ''),
                                    data.get('comparison_count', ''),
                                    comp.get('recid', ''), comp.get('cm_doc_id', ''),
                                    comp.get('rev_doc_id', ''), comp.get('vendor', ''),
                                    comp.get('diff_file_path', ''), comp.get('changed_features', ''),
                                    comp.get('input_features', ''), comp.get('cm_line_pn', ''),
                                    comp.get('rev_line_pn', ''), comp.get('failed_line_cm', ''),
                                    comp.get('failed_line_rev', ''), comp.get('failed_at_structured', ''),
                                    ''
                                ]
                                writer.writerow(row)
                        else:
                            # No comparison data found
                            row = [
                                row_number, result['url1'], result['url2'], data['status'],
                                data.get('doc1_id', ''), data.get('doc2_id', ''), '',
                                '', '', '', '', '', '', '', '', '', '', '', '',
                                data.get('message', data.get('error', ''))
                            ]
                            writer.writerow(row)
                    else:
                        # Error case (including invalid format)
                        row = [
                            row_number, result.get('url1', ''), result.get('url2', ''), 'error',
                            '', '', '', '', '', '', '', '', '', '', '', '', '', '', '',
                            result.get('error', 'Unknown error')
                        ]
                        writer.writerow(row)

            logger.info(f"Generated results file: {results_filepath}")
            return results_filename

        except Exception as e:
            logger.error(f"Error generating results file: {str(e)}")
            raise Exception(f"Failed to generate results file: {str(e)}")
    
    def _is_valid_url(self, url: str) -> bool:
        """
        Validate URL format.
        
        Args:
            url (str): URL to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            result = urllib.parse.urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
