#!/usr/bin/env python3
"""
Startup script for the PDF Comparison Tool.
This script provides a simple way to start the application with proper configuration.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 7):
        print("Error: Python 3.7 or higher is required.")
        print(f"Current version: {sys.version}")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ['flask', 'oracledb', 'werkzeug']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nInstall missing packages with:")
        print("  pip install -r requirements.txt")
        return False
    
    return True

def setup_environment():
    """Set up environment variables if not already set."""
    env_vars = {
        'FLASK_ENV': 'development',
        'HOST': '0.0.0.0',
        'PORT': '5000'
    }
    
    for var, default_value in env_vars.items():
        if var not in os.environ:
            os.environ[var] = default_value

def create_directories():
    """Create necessary directories."""
    directories = ['uploads', 'results', 'logs']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Directory '{directory}' ready")

def main():
    """Main startup function."""
    print("PDF Comparison Tool - Startup Script")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check dependencies
    print("Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    print("✓ All dependencies are installed")
    
    # Setup environment
    setup_environment()
    print("✓ Environment configured")
    
    # Create directories
    print("Setting up directories...")
    create_directories()
    
    # Display configuration
    print("\nConfiguration:")
    print(f"  Environment: {os.environ.get('FLASK_ENV', 'development')}")
    print(f"  Host: {os.environ.get('HOST', '0.0.0.0')}")
    print(f"  Port: {os.environ.get('PORT', '5000')}")
    
    # Start the application
    print("\nStarting PDF Comparison Tool...")
    print("Press Ctrl+C to stop the server")
    print("-" * 40)
    
    try:
        # Import and run the Flask app
        from app import app
        
        host = os.environ.get('HOST', '0.0.0.0')
        port = int(os.environ.get('PORT', 5000))
        debug = os.environ.get('FLASK_ENV') == 'development'
        
        app.run(host=host, port=port, debug=debug)
        
    except KeyboardInterrupt:
        print("\n\nShutting down PDF Comparison Tool...")
        print("Goodbye!")
    except Exception as e:
        print(f"\nError starting application: {str(e)}")
        print("Check the logs for more details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
