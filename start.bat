@echo off
echo PDF Comparison Tool - Windows Startup Script
echo ============================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7 or higher from https://python.org
    pause
    exit /b 1
)

REM Install dependencies if requirements.txt exists
if exist requirements.txt (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Create necessary directories
if not exist uploads mkdir uploads
if not exist results mkdir results
if not exist logs mkdir logs

REM Set environment variables
set FLASK_ENV=development
set HOST=0.0.0.0
set PORT=5000

echo.
echo Starting PDF Comparison Tool...
echo Server will be available at http://localhost:5000
echo Press Ctrl+C to stop the server
echo.

REM Start the application
python app.py

pause
