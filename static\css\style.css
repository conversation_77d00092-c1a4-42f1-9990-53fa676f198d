/* Windows XP Style */
body {
    font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
    font-size: 11px;
    background: #ece9d8;
    margin: 0;
    padding: 20px;
}

.upload-container {
    background: #f0f0f0;
    border: 2px outset #f0f0f0;
    padding: 20px;
    width: 500px;
    margin: 50px auto;
}

.upload-container h2 {
    color: #000080;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
}

.upload-container p {
    color: #000;
    margin-bottom: 20px;
}

.file-section {
    margin-bottom: 20px;
}

.file-section label {
    display: inline-block;
    width: 40px;
    color: #000;
    font-weight: normal;
}

.file-section input[type="file"] {
    width: 300px;
    border: 1px inset #f0f0f0;
    padding: 2px;
    background: white;
    font-family: <PERSON>hom<PERSON>, <PERSON>l, sans-serif;
    font-size: 11px;
}

.file-section button {
    background: #ece9d8;
    border: 1px outset #ece9d8;
    padding: 4px 12px;
    font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
    font-size: 11px;
    cursor: pointer;
    margin-left: 10px;
}

.file-section button:hover {
    background: #ddd6c7;
}

.file-section button:active {
    border: 1px inset #ece9d8;
}

.file-section button:disabled {
    color: #808080;
    cursor: default;
    background: #ece9d8;
}

#statusDiv {
    margin: 20px 0;
    padding: 10px;
    background: #fff;
    border: 1px inset #f0f0f0;
}

#statusText {
    color: #000;
    font-weight: bold;
}

#resultsDiv {
    margin: 20px 0;
    padding: 10px;
    background: #fff;
    border: 1px inset #f0f0f0;
}

#resultsText {
    color: #000;
    margin-bottom: 10px;
}

#downloadBtn, #resetBtn {
    background: #ece9d8;
    border: 1px outset #ece9d8;
    padding: 4px 12px;
    font-family: Tahoma, Arial, sans-serif;
    font-size: 11px;
    cursor: pointer;
    margin-right: 10px;
}

#downloadBtn:hover, #resetBtn:hover {
    background: #ddd6c7;
}

#downloadBtn:active, #resetBtn:active {
    border: 1px inset #ece9d8;
}




