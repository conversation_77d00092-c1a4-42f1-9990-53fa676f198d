/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    color: #667eea;
}

.nav {
    display: flex;
    gap: 1rem;
}

.nav-link {
    text-decoration: none;
    color: #4a5568;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    background: #667eea;
    color: white;
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 140px);
}

/* Upload Section */
.upload-section {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.upload-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.upload-header h2 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.upload-description {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Instructions Panel */
.instructions-panel {
    padding: 2rem;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.instructions-panel h3 {
    color: #2d3748;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.instructions-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.format-example h4,
.requirements h4 {
    color: #4a5568;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.code-block {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.requirements ul {
    list-style: none;
}

.requirements li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #4a5568;
}

.requirements li i {
    color: #48bb78;
}

/* Upload Form */
.upload-form-container {
    padding: 2rem;
}

.upload-form {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.file-input-container {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.file-input-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 150px;
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    background: #f7fafc;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.file-input-label:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.file-input-label i {
    font-size: 2rem;
    color: #a0aec0;
    margin-bottom: 0.5rem;
}

.file-input-text {
    color: #4a5568;
    font-weight: 500;
}

#fileInput {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: #e6fffa;
    border: 1px solid #81e6d9;
    border-radius: 8px;
    color: #234e52;
    margin-top: 1rem;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 200px;
    justify-content: center;
}

.upload-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.upload-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Progress Section */
.progress-section {
    padding: 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.progress-header {
    text-align: center;
    margin-bottom: 2rem;
}

.progress-header h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    font-weight: 600;
    color: #4a5568;
    min-width: 40px;
}

.progress-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.detail-label {
    font-weight: 600;
    color: #4a5568;
}

.detail-value {
    color: #2d3748;
}

/* Results and Error Sections */
.results-section,
.error-section {
    padding: 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.results-header,
.error-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.results-header h3 {
    color: #38a169;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.error-header h3 {
    color: #e53e3e;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.results-summary {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    margin-bottom: 1.5rem;
}

.error-message {
    background: #fed7d7;
    color: #742a2a;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #feb2b2;
    margin-bottom: 1.5rem;
    white-space: pre-line;
}

.results-actions,
.error-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.download-btn,
.reset-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.download-btn {
    background: #38a169;
    color: white;
}

.download-btn:hover {
    background: #2f855a;
    transform: translateY(-2px);
}

.reset-btn {
    background: #4a5568;
    color: white;
}

.reset-btn:hover {
    background: #2d3748;
    transform: translateY(-2px);
}

/* Flash Messages */
.flash-messages {
    margin-bottom: 1rem;
}

.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-error {
    background: #fed7d7;
    color: #742a2a;
    border: 1px solid #feb2b2;
}

.alert-success {
    background: #c6f6d5;
    color: #22543d;
    border: 1px solid #9ae6b4;
}

.alert-info {
    background: #bee3f8;
    color: #2a4365;
    border: 1px solid #90cdf4;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    text-align: center;
    padding: 1rem;
    color: #4a5568;
    border-top: 1px solid #e2e8f0;
}

/* Summary Grid */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.summary-label {
    font-weight: 600;
    color: #4a5568;
}

.summary-value {
    font-weight: 700;
    color: #2d3748;
}

.summary-value.success {
    color: #38a169;
}

.summary-value.error {
    color: #e53e3e;
}

/* Responsive Design */
@media (max-width: 768px) {
    .instructions-content {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .progress-details {
        grid-template-columns: 1fr;
    }

    .results-actions,
    .error-actions {
        flex-direction: column;
        align-items: center;
    }

    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }
}
