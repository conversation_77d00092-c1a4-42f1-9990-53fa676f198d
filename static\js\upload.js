// Simple upload functionality

document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadForm = document.getElementById('uploadForm');
    const statusDiv = document.getElementById('statusDiv');
    const statusText = document.getElementById('statusText');
    const resultsDiv = document.getElementById('resultsDiv');
    const resultsText = document.getElementById('resultsText');
    const downloadBtn = document.getElementById('downloadBtn');
    const resetBtn = document.getElementById('resetBtn');

    // File input change handler
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                uploadBtn.disabled = false;
            } else {
                uploadBtn.disabled = true;
            }
        });
    }

    // Form submit handler
    if (uploadForm) {
        uploadForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const file = fileInput.files[0];
            if (!file) {
                alert('Please select a file to upload.');
                return;
            }

            uploadFile(file);
        });
    }

    // Reset button handler
    if (resetBtn) {
        resetBtn.addEventListener('click', function() {
            fileInput.value = '';
            uploadBtn.disabled = true;
            statusDiv.style.display = 'none';
            resultsDiv.style.display = 'none';
        });
    }

    // Download button handler
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            const filename = this.getAttribute('data-filename');
            if (filename) {
                window.location.href = `/download/${filename}`;
            }
        });
    }

    function uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        // Show status
        statusDiv.style.display = 'block';
        statusText.textContent = 'Uploading and processing file...';
        resultsDiv.style.display = 'none';

        // Disable upload button
        uploadBtn.disabled = true;
        uploadBtn.textContent = 'Processing...';

        // Upload file
        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                statusText.textContent = 'File uploaded. Processing URL pairs...';
                // Start processing
                processFile(data.filename);
            } else {
                throw new Error(data.error || 'Upload failed');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            alert('Error: ' + (error.message || 'An error occurred during file upload.'));
            resetForm();
        });
    }

    function processFile(filename) {
        fetch(`/process/${filename}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                statusDiv.style.display = 'none';
                resultsDiv.style.display = 'block';
                resultsText.textContent = `Processing complete! Total: ${data.total_processed}, Successful: ${data.successful}, Failed: ${data.failed}`;
                downloadBtn.setAttribute('data-filename', data.results_file);
            } else {
                throw new Error(data.error || 'Processing failed');
            }
        })
        .catch(error => {
            console.error('Processing error:', error);
            alert('Error: ' + (error.message || 'An error occurred during file processing.'));
        })
        .finally(() => {
            uploadBtn.disabled = false;
            uploadBtn.textContent = 'Process File';
        });
    }

    function resetForm() {
        uploadBtn.disabled = false;
        uploadBtn.textContent = 'Process File';
        statusDiv.style.display = 'none';
        resultsDiv.style.display = 'none';
    }
});
