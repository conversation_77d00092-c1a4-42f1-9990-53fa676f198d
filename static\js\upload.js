// Upload functionality for the PDF Comparison Tool

document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadForm = document.getElementById('uploadForm');
    const fileInputLabel = document.querySelector('.file-input-label');
    
    // File input change handler
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            
            if (file) {
                // Validate file type
                const allowedTypes = ['.tsv', '.txt'];
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                
                if (!allowedTypes.includes(fileExtension)) {
                    showError('Invalid file type. Please select a TSV or TXT file.');
                    resetFileInput();
                    return;
                }
                
                // Validate file size (16MB limit)
                const maxSize = 16 * 1024 * 1024; // 16MB
                if (file.size > maxSize) {
                    showError('File size exceeds 16MB limit. Please select a smaller file.');
                    resetFileInput();
                    return;
                }
                
                // Display file info
                if (fileName) fileName.textContent = file.name;
                if (fileSize) fileSize.textContent = `(${formatFileSize(file.size)})`;
                
                showElement(fileInfo);
                
                // Enable upload button
                if (uploadBtn) {
                    uploadBtn.disabled = false;
                }
                
                // Update label appearance
                if (fileInputLabel) {
                    fileInputLabel.style.borderColor = '#38a169';
                    fileInputLabel.style.backgroundColor = '#f0fff4';
                }
                
                // Validate TSV format (basic client-side check)
                validateTSVFile(file);
                
            } else {
                resetFileInput();
            }
        });
    }
    
    // Form submit handler
    if (uploadForm) {
        uploadForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const file = fileInput.files[0];
            if (!file) {
                showError('Please select a file to upload.');
                return;
            }
            
            uploadFile(file);
        });
    }
    
    // Drag and drop functionality
    if (fileInputLabel) {
        fileInputLabel.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = '#667eea';
            this.style.backgroundColor = '#edf2f7';
        });
        
        fileInputLabel.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '#cbd5e0';
            this.style.backgroundColor = '#f7fafc';
        });
        
        fileInputLabel.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#cbd5e0';
            this.style.backgroundColor = '#f7fafc';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                fileInput.dispatchEvent(new Event('change'));
            }
        });
    }
    
    function resetFileInput() {
        if (fileInput) fileInput.value = '';
        if (uploadBtn) uploadBtn.disabled = true;
        if (fileInfo) hideElement(fileInfo);
        if (fileInputLabel) {
            fileInputLabel.style.borderColor = '#cbd5e0';
            fileInputLabel.style.backgroundColor = '#f7fafc';
        }
    }
    
    function validateTSVFile(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const content = e.target.result;
            const lines = content.split('\n').filter(line => line.trim() !== '');
            
            if (lines.length === 0) {
                showError('File appears to be empty.');
                resetFileInput();
                return;
            }
            
            // Check first few lines for tab separation
            let hasTabSeparation = false;
            for (let i = 0; i < Math.min(5, lines.length); i++) {
                if (lines[i].includes('\t')) {
                    hasTabSeparation = true;
                    break;
                }
            }
            
            if (!hasTabSeparation) {
                showError('File does not appear to be tab-separated. Please ensure URLs are separated by tab characters.');
                resetFileInput();
                return;
            }
            
            console.log(`File validation passed. Found ${lines.length} lines.`);
        };
        
        reader.onerror = function() {
            showError('Error reading file. Please try again.');
            resetFileInput();
        };
        
        // Read first 1KB for validation
        const blob = file.slice(0, 1024);
        reader.readAsText(blob);
    }
    
    function uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        // Show progress section
        const progressSection = document.getElementById('progressSection');
        const processingFileName = document.getElementById('processingFileName');
        
        if (processingFileName) {
            processingFileName.textContent = file.name;
        }
        
        showElement(progressSection);
        updateProgress(10, 'Uploading file...');
        
        // Disable upload button
        if (uploadBtn) {
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }
        
        // Upload file
        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateProgress(50, 'File uploaded successfully. Processing...');
                
                // Start processing
                processFile(data.filename);
            } else {
                throw new Error(data.error || 'Upload failed');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            showError(error.message || 'An error occurred during file upload.');
            resetUploadButton();
        });
    }
    
    function processFile(filename) {
        updateProgress(60, 'Processing URL pairs...');
        
        fetch(`/process/${filename}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateProgress(100, 'Processing complete!');
                
                // Show results after a brief delay
                setTimeout(() => {
                    showResults(data);
                }, 1000);
            } else {
                throw new Error(data.error || 'Processing failed');
            }
        })
        .catch(error => {
            console.error('Processing error:', error);
            showError(error.message || 'An error occurred during file processing.');
        })
        .finally(() => {
            resetUploadButton();
        });
    }
    
    function resetUploadButton() {
        if (uploadBtn) {
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload and Process';
        }
    }
});
