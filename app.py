from flask import Flask, render_template, request, jsonify, send_file, flash, redirect, url_for
import os
import csv
import io
import tempfile
from datetime import datetime
from werkzeug.utils import secure_filename
import logging
from logging.handlers import RotatingFileHandler
from database_service import DatabaseService
from file_processor import FileProcessor
from config import config

def create_app(config_name=None):
    """Application factory pattern."""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    app = Flask(__name__)
    app.config.from_object(config.get(config_name, config['default']))

    # Initialize configuration
    config[config_name].init_app(app)

    # Configure logging
    configure_logging(app)

    # Initialize services
    try:
        db_service = DatabaseService(app.config)
        file_processor = FileProcessor(app.config)
        logger = logging.getLogger(__name__)
        logger.info("Application services initialized successfully")
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to initialize services: {str(e)}")
        raise

    return app, db_service, file_processor

def configure_logging(app):
    """Configure application logging."""
    if not app.debug and not app.testing:
        # File logging for production
        if not os.path.exists('logs'):
            os.mkdir('logs')

        file_handler = RotatingFileHandler(
            'logs/pdf_comparison.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

        app.logger.setLevel(logging.INFO)
        app.logger.info('PDF Comparison Tool startup')
    else:
        # Console logging for development
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s %(levelname)s: %(message)s'
        )

# Create application instance
app, db_service, file_processor = create_app()
logger = logging.getLogger(__name__)

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors."""
    logger.warning(f"404 error: {request.url}")
    return jsonify({'error': 'Resource not found'}), 404

@app.errorhandler(413)
def file_too_large_error(error):
    """Handle file too large errors."""
    logger.warning(f"File too large error: {request.url}")
    return jsonify({'error': 'File size exceeds the maximum limit of 16MB'}), 413

@app.errorhandler(500)
def internal_error(error):
    """Handle internal server errors."""
    logger.error(f"Internal server error: {str(error)}")
    return jsonify({'error': 'An internal server error occurred'}), 500

@app.errorhandler(Exception)
def handle_exception(error):
    """Handle unexpected exceptions."""
    logger.error(f"Unexpected error: {str(error)}", exc_info=True)
    return jsonify({'error': 'An unexpected error occurred'}), 500

@app.route('/')
def index():
    """Main page with file upload form and instructions."""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and initiate processing."""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file selected'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not file_processor.is_valid_file(file):
            return jsonify({'error': 'Invalid file type. Please upload a TSV file.'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Validate TSV format
        validation_result = file_processor.validate_tsv_format(filepath)
        if not validation_result['valid']:
            os.remove(filepath)  # Clean up invalid file
            return jsonify({'error': validation_result['error']}), 400
        
        return jsonify({
            'success': True,
            'filename': filename,
            'message': f'File uploaded successfully. Found {validation_result["row_count"]} URL pairs.'
        })
        
    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        return jsonify({'error': 'An error occurred during file upload'}), 500

@app.route('/process/<filename>')
def process_file(filename):
    """Process the uploaded TSV file and return results."""
    try:
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if not os.path.exists(filepath):
            return jsonify({'error': 'File not found'}), 404

        # Parse TSV file (now returns all rows including invalid ones)
        rows_data = file_processor.parse_tsv_file(filepath)

        # Process all rows
        results = []
        total_rows = len(rows_data)
        successful = 0
        failed = 0

        for row_data in rows_data:
            if row_data['valid']:
                # Process valid URL pairs
                try:
                    db_result = db_service.compare_pdfs(row_data['url1'], row_data['url2'])
                    results.append({
                        'row_number': row_data['row_number'],
                        'url1': row_data['url1'],
                        'url2': row_data['url2'],
                        'status': 'success',
                        'data': db_result
                    })
                    successful += 1
                except Exception as e:
                    logger.error(f"Error processing row {row_data['row_number']}: {str(e)}")
                    results.append({
                        'row_number': row_data['row_number'],
                        'url1': row_data['url1'],
                        'url2': row_data['url2'],
                        'status': 'error',
                        'error': str(e)
                    })
                    failed += 1
            else:
                # Include invalid rows in results
                results.append({
                    'row_number': row_data['row_number'],
                    'url1': row_data['url1'],
                    'url2': row_data['url2'],
                    'status': 'error',
                    'error': row_data['error']
                })
                failed += 1

        # Generate results file
        results_filename = file_processor.generate_results_file(results, filename)

        # Clean up uploaded file
        os.remove(filepath)

        return jsonify({
            'success': True,
            'results_file': results_filename,
            'total_processed': total_rows,
            'successful': successful,
            'failed': failed
        })

    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        return jsonify({'error': 'An error occurred during file processing'}), 500

@app.route('/download/<filename>')
def download_file(filename):
    """Download the processed results file."""
    try:
        filepath = os.path.join('results', filename)
        if not os.path.exists(filepath):
            return jsonify({'error': 'Results file not found'}), 404
        
        return send_file(filepath, as_attachment=True, download_name=filename)
        
    except Exception as e:
        logger.error(f"Download error: {str(e)}")
        return jsonify({'error': 'An error occurred during file download'}), 500

@app.route('/status/<filename>')
def get_status(filename):
    """Get processing status for a file."""
    # This could be enhanced with a proper job queue system
    # For now, we'll return a simple status
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if os.path.exists(filepath):
        return jsonify({'status': 'processing'})
    else:
        return jsonify({'status': 'completed'})

if __name__ == '__main__':
    # Get configuration from environment
    config_name = os.environ.get('FLASK_ENV', 'development')
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')

    logger.info(f"Starting PDF Comparison Tool in {config_name} mode")
    logger.info(f"Server will be available at http://{host}:{port}")

    app.run(debug=(config_name == 'development'), host=host, port=port)
