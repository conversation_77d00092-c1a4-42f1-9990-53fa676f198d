// Main JavaScript functionality for the PDF Comparison Tool

// Utility functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showElement(element) {
    if (element) {
        element.style.display = 'block';
    }
}

function hideElement(element) {
    if (element) {
        element.style.display = 'none';
    }
}

function updateProgress(percentage, text = null) {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    
    if (progressFill) {
        progressFill.style.width = percentage + '%';
    }
    
    if (progressText) {
        progressText.textContent = percentage + '%';
    }
    
    if (text) {
        const statusText = document.getElementById('statusText');
        if (statusText) {
            statusText.textContent = text;
        }
    }
}

function showError(message) {
    const errorSection = document.getElementById('errorSection');
    const errorMessage = document.getElementById('errorMessage');
    const progressSection = document.getElementById('progressSection');
    const resultsSection = document.getElementById('resultsSection');
    
    hideElement(progressSection);
    hideElement(resultsSection);
    
    if (errorMessage) {
        errorMessage.textContent = message;
    }
    
    showElement(errorSection);
}

function showResults(data) {
    const resultsSection = document.getElementById('resultsSection');
    const resultsSummary = document.getElementById('resultsSummary');
    const progressSection = document.getElementById('progressSection');
    const errorSection = document.getElementById('errorSection');
    
    hideElement(progressSection);
    hideElement(errorSection);
    
    if (resultsSummary && data) {
        resultsSummary.innerHTML = `
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-label">Total Processed:</div>
                    <div class="summary-value">${data.total_processed || 0}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Successful:</div>
                    <div class="summary-value success">${data.successful || 0}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Failed:</div>
                    <div class="summary-value error">${data.failed || 0}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Results File:</div>
                    <div class="summary-value">${data.results_file || 'N/A'}</div>
                </div>
            </div>
        `;
        
        // Store results file name for download
        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn && data.results_file) {
            downloadBtn.setAttribute('data-filename', data.results_file);
        }
    }
    
    showElement(resultsSection);
}

function resetForm() {
    const uploadForm = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const uploadBtn = document.getElementById('uploadBtn');
    const progressSection = document.getElementById('progressSection');
    const resultsSection = document.getElementById('resultsSection');
    const errorSection = document.getElementById('errorSection');
    
    // Reset form
    if (uploadForm) {
        uploadForm.reset();
    }
    
    // Reset file input display
    if (fileInfo) {
        hideElement(fileInfo);
    }
    
    // Reset upload button
    if (uploadBtn) {
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload and Process';
    }
    
    // Hide all sections
    hideElement(progressSection);
    hideElement(resultsSection);
    hideElement(errorSection);
    
    // Reset progress
    updateProgress(0, 'Ready');
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('PDF Comparison Tool initialized');
    
    // Add any global event listeners or initialization here
    const resetBtns = document.querySelectorAll('.reset-btn, #errorResetBtn');
    resetBtns.forEach(btn => {
        btn.addEventListener('click', resetForm);
    });
    
    // Download button handler
    const downloadBtn = document.getElementById('downloadBtn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            const filename = this.getAttribute('data-filename');
            if (filename) {
                window.location.href = `/download/${filename}`;
            }
        });
    }
});
