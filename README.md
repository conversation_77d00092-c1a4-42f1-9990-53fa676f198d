# PDF Comparison Tool

A web-based application for comparing PDF documents using URL pairs from TSV files. This tool processes tab-separated values files containing PDF URLs and performs database queries to compare the documents.

## Features

- **File Upload**: Upload TSV files with URL pairs
- **Format Validation**: Automatic validation of TSV format and URL structure
- **Progress Tracking**: Real-time progress updates during processing
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Results Download**: Download processed results as TSV files
- **Responsive Design**: Modern, mobile-friendly user interface

## Requirements

- Python 3.7+
- Oracle Database access
- Flask web framework
- Oracle Database client libraries

## Installation

1. **Clone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure database connection**:
   - Update the database connection details in `database_service.py`
   - Ensure Oracle client libraries are installed and configured

4. **Create necessary directories**:
   ```bash
   mkdir uploads results templates static
   ```

## Usage

### Starting the Application

1. **Run the Flask application**:
   ```bash
   python app.py
   ```

2. **Access the web interface**:
   - Open your browser and navigate to `http://localhost:5000`

### Using the Tool

1. **Prepare your TSV file**:
   - Create a tab-separated values file
   - Each row should contain exactly 2 URLs separated by a tab character
   - Example format:
     ```
     http://example.com/pdf1.pdf	http://example.com/pdf2.pdf
     http://example.com/pdf3.pdf	http://example.com/pdf4.pdf
     ```

2. **Upload and process**:
   - Click "Choose TSV File" or drag and drop your file
   - The system will validate the file format
   - Click "Upload and Process" to start processing
   - Monitor the progress in real-time

3. **Download results**:
   - Once processing is complete, click "Download Results"
   - The results file will contain comparison data in TSV format

## File Format Requirements

### Input TSV File
- **Extension**: `.tsv` or `.txt`
- **Encoding**: UTF-8
- **Separator**: Tab character (`\t`)
- **Structure**: Two URLs per row
- **Size limit**: 16MB maximum

### Example Input
```
http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618801/ech_/manual/skupage.014626.pdf	http://download.siliconexpert.com/pdfs2/2025/9/19/12/10/5/910944574/ech_/manual/skupage.014626.pdf
http://example.com/another-pdf1.pdf	http://example.com/another-pdf2.pdf
```

### Output TSV File
The results file contains the following columns:
- `URL1`, `URL2`: Original input URLs
- `Status`: Processing status (success/error/no_comparison)
- `Doc1_ID`, `Doc2_ID`: Database document IDs
- `Comparison_Count`: Number of comparisons found
- Database comparison fields: `RECID`, `CM_DOC_ID`, `REV_DOC_ID`, `VENDOR`, etc.
- `Error_Message`: Error details if processing failed

## Project Structure

```
pdf-comparison-tool/
├── app.py                 # Main Flask application
├── database_service.py    # Database connection and queries
├── file_processor.py      # File handling and validation
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── templates/
│   ├── base.html         # Base template
│   └── index.html        # Main page template
├── static/
│   ├── css/
│   │   └── style.css     # Application styles
│   └── js/
│       ├── main.js       # Main JavaScript functionality
│       └── upload.js     # Upload-specific JavaScript
├── uploads/              # Temporary file storage
└── results/              # Generated results files
```

## Configuration

### Database Configuration
Update the database connection settings in `database_service.py`:

```python
# Update these values for your Oracle database
dsn = oracledb.makedsn("your-host", 1521, service_name="your-service")
conn = oracledb.connect(user="your-user", password="your-password", dsn=dsn)
```

### Application Configuration
Modify settings in `app.py`:

```python
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # File size limit
```

## Error Handling

The application handles various error scenarios:

- **Invalid file format**: Non-TSV files or incorrect structure
- **Database connection issues**: Connection failures or query errors
- **Missing documents**: URLs not found in the database
- **File size limits**: Files exceeding the 16MB limit
- **Network issues**: Upload or processing failures

## Security Considerations

- Change the default secret key in production
- Implement proper authentication if needed
- Validate and sanitize all user inputs
- Use HTTPS in production environments
- Regularly update dependencies

## Troubleshooting

### Common Issues

1. **Database connection errors**:
   - Verify Oracle client libraries are installed
   - Check database connection parameters
   - Ensure network connectivity to the database

2. **File upload issues**:
   - Check file format (must be TSV)
   - Verify file size is under 16MB
   - Ensure proper tab separation

3. **Processing errors**:
   - Check application logs for detailed error messages
   - Verify URLs are accessible and valid
   - Ensure database contains the referenced documents

### Logging

The application uses Python's logging module. Check the console output for detailed error messages and processing information.

## License

This project is provided as-is for internal use. Please ensure compliance with your organization's policies regarding database access and data handling.
