import os
from datetime import timedelta

class Config:
    """Base configuration class."""
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # File Upload Configuration
    UPLOAD_FOLDER = 'uploads'
    RESULTS_FOLDER = 'results'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'tsv', 'txt'}
    
    # Database Configuration
    DB_HOST = os.environ.get('DB_HOST') or '**************'
    DB_PORT = int(os.environ.get('DB_PORT') or 1521)
    DB_SERVICE_NAME = os.environ.get('DB_SERVICE_NAME') or 'scrubbing'
    DB_USER = os.environ.get('DB_USER') or 'READ_ONLY'
    DB_PASSWORD = os.environ.get('DB_PASSWORD') or 'READ_ONLY'
    
    # Processing Configuration
    MAX_PROCESSING_TIME = timedelta(minutes=30)  # Maximum processing time
    BATCH_SIZE = 100  # Process URLs in batches
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'app.log'
    
    # Security Configuration
    SESSION_TIMEOUT = timedelta(hours=2)
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration."""
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.RESULTS_FOLDER, exist_ok=True)

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    TESTING = False
    
    # Override with more secure defaults for production
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """Testing configuration."""
    DEBUG = True
    TESTING = True
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
