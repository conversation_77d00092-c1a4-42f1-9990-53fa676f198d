#!/usr/bin/env python3
"""
Simple test script to verify the PDF Comparison Tool functionality.
This script creates a sample TSV file and tests basic functionality.
"""

import os
import tempfile
import requests
import time

def create_sample_tsv():
    """Create a sample TSV file for testing."""
    sample_data = [
        "http://download.siliconexpert.com/pdfs2/2025/6/17/11/34/37/1241618801/ech_/manual/skupage.014626.pdf\thttp://download.siliconexpert.com/pdfs2/2025/9/19/12/10/5/910944574/ech_/manual/skupage.014626.pdf",
        "http://example.com/test1.pdf\thttp://example.com/test2.pdf",
        "http://example.com/test3.pdf\thttp://example.com/test4.pdf"
    ]
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.tsv', delete=False) as f:
        for line in sample_data:
            f.write(line + '\n')
        return f.name

def test_application():
    """Test the Flask application."""
    base_url = "http://localhost:5000"
    
    print("Testing PDF Comparison Tool...")
    
    # Test 1: Check if application is running
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print("✓ Application is running and accessible")
        else:
            print(f"✗ Application returned status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ Cannot connect to application: {str(e)}")
        print("Make sure the Flask application is running with: python app.py")
        return False
    
    # Test 2: Create and upload sample file
    sample_file = create_sample_tsv()
    try:
        with open(sample_file, 'rb') as f:
            files = {'file': ('test_sample.tsv', f, 'text/tab-separated-values')}
            response = requests.post(f"{base_url}/upload", files=files, timeout=30)
            
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✓ File upload successful")
                filename = data.get('filename')
                
                # Test 3: Process the file
                if filename:
                    print("Testing file processing...")
                    response = requests.get(f"{base_url}/process/{filename}", timeout=60)
                    
                    if response.status_code == 200:
                        result_data = response.json()
                        if result_data.get('success'):
                            print("✓ File processing completed successfully")
                            print(f"  - Total processed: {result_data.get('total_processed', 0)}")
                            print(f"  - Successful: {result_data.get('successful', 0)}")
                            print(f"  - Failed: {result_data.get('failed', 0)}")
                            print(f"  - Results file: {result_data.get('results_file', 'N/A')}")
                        else:
                            print(f"✗ File processing failed: {result_data.get('error', 'Unknown error')}")
                    else:
                        print(f"✗ Processing request failed with status: {response.status_code}")
                        print(f"Response: {response.text}")
            else:
                print(f"✗ File upload failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"✗ Upload request failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"✗ Request failed: {str(e)}")
    finally:
        # Clean up temporary file
        if os.path.exists(sample_file):
            os.unlink(sample_file)
    
    return True

def check_dependencies():
    """Check if required dependencies are available."""
    print("Checking dependencies...")
    
    try:
        import flask
        print(f"✓ Flask {flask.__version__} is installed")
    except ImportError:
        print("✗ Flask is not installed. Run: pip install Flask")
        return False
    
    try:
        import oracledb
        print(f"✓ oracledb is installed")
    except ImportError:
        print("✗ oracledb is not installed. Run: pip install oracledb")
        return False
    
    try:
        import requests
        print(f"✓ requests is installed")
    except ImportError:
        print("✗ requests is not installed. Run: pip install requests")
        return False
    
    return True

if __name__ == "__main__":
    print("PDF Comparison Tool - Test Script")
    print("=" * 40)
    
    if not check_dependencies():
        print("\nPlease install missing dependencies and try again.")
        exit(1)
    
    print("\nStarting application tests...")
    print("Note: Make sure the Flask application is running before running this test.")
    print("Start the application with: python app.py")
    print()
    
    # Wait a moment for user to start the application if needed
    input("Press Enter when the application is running...")
    
    success = test_application()
    
    if success:
        print("\n✓ Basic functionality test completed!")
        print("\nNext steps:")
        print("1. Open http://localhost:5000 in your browser")
        print("2. Upload a TSV file with URL pairs")
        print("3. Monitor the processing progress")
        print("4. Download the results file")
    else:
        print("\n✗ Tests failed. Please check the application logs for more details.")
