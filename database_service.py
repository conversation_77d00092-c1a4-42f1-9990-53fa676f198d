import oracledb
import logging
from typing import Optional, Dict, List, Any
import urllib.parse

logger = logging.getLogger(__name__)

class DatabaseService:
    """Service class for handling database operations related to PDF comparison."""

    def __init__(self, config=None):
        """Initialize database connection."""
        self.config = config or {}
        self.dsn = None
        self.conn = None
        self._connect()

    def _connect(self):
        """Establish database connection."""
        try:
            # Build DSN from configuration
            host = self.config.get('DB_HOST', '**************')
            port = self.config.get('DB_PORT', 1521)
            service_name = self.config.get('DB_SERVICE_NAME', 'scrubbing')
            user = self.config.get('DB_USER', 'READ_ONLY')
            password = self.config.get('DB_PASSWORD', 'READ_ONLY')

            self.dsn = oracledb.makedsn(host, port, service_name=service_name)

            # Connect with retry logic
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.conn = oracledb.connect(user=user, password=password, dsn=self.dsn)
                    logger.info(f"Database connection established successfully (attempt {attempt + 1})")
                    break
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    logger.warning(f"Connection attempt {attempt + 1} failed: {str(e)}")

        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            raise Exception(f"Database connection failed: {str(e)}")
    
    def _ensure_connection(self):
        """Ensure database connection is active."""
        try:
            if self.conn is None or not self.conn.ping():
                logger.info("Reconnecting to database...")
                self._connect()
        except Exception as e:
            logger.error(f"Connection check failed: {str(e)}")
            self._connect()
    
    def get_doc_id_by_url(self, url: str) -> Optional[int]:
        """
        Get document ID by URL.
        
        Args:
            url (str): The PDF URL to search for
            
        Returns:
            Optional[int]: Document ID if found, None otherwise
        """
        try:
            self._ensure_connection()
            
            # Validate URL format
            if not self._is_valid_url(url):
                logger.warning(f"Invalid URL format: {url}")
                return None
            
            with self.conn.cursor() as cur:
                cur.execute("""
                    SELECT doc.id
                    FROM automation2.document@auto doc
                    JOIN automation2.pdf@auto pdf ON doc.pdf_id = pdf.id
                    WHERE pdf.se_url = :url
                """, [url])
                
                row = cur.fetchone()
                doc_id = row[0] if row else None
                
                if doc_id:
                    logger.info(f"Found document ID {doc_id} for URL: {url}")
                else:
                    logger.warning(f"No document found for URL: {url}")
                
                return doc_id
                
        except Exception as e:
            logger.error(f"Error getting document ID for URL {url}: {str(e)}")
            raise Exception(f"Failed to retrieve document ID: {str(e)}")
    
    def compare_pdfs(self, url1: str, url2: str) -> Dict[str, Any]:
        """
        Compare two PDFs by their URLs.
        
        Args:
            url1 (str): First PDF URL
            url2 (str): Second PDF URL
            
        Returns:
            Dict[str, Any]: Comparison results or error information
        """
        try:
            self._ensure_connection()
            
            # Get document IDs
            doc1 = self.get_doc_id_by_url(url1)
            doc2 = self.get_doc_id_by_url(url2)
            
            if not doc1:
                return {
                    'status': 'error',
                    'error': f'Document not found for URL1: {url1}',
                    'url1': url1,
                    'url2': url2
                }
            
            if not doc2:
                return {
                    'status': 'error',
                    'error': f'Document not found for URL2: {url2}',
                    'url1': url1,
                    'url2': url2
                }
            
            # Query comparison data
            with self.conn.cursor() as cur:
                cur.execute("""
                    SELECT RECID, CM_DOC_ID, REV_DOC_ID, VENDOR, DIFF_FILE_PATH, 
                           CHANGED_FEATURES, INPUT_FEATURES, CM_LINE_PN, REV_LINE_PN, 
                           FAILED_LINE_CM, FAILED_LINE_REV, FAILED_AT_STRUCTURED
                    FROM automation2.new_compare_pdfs@auto
                    WHERE rev_doc_id = :doc1
                      AND cm_doc_id = :doc2
                """, [doc1, doc2])
                
                rows = cur.fetchall()
                
                if not rows:
                    return {
                        'status': 'no_comparison',
                        'message': 'No comparison data found for these documents',
                        'url1': url1,
                        'url2': url2,
                        'doc1_id': doc1,
                        'doc2_id': doc2
                    }
                
                # Format results
                comparisons = []
                for row in rows:
                    comparison = {
                        'recid': row[0],
                        'cm_doc_id': row[1],
                        'rev_doc_id': row[2],
                        'vendor': row[3],
                        'diff_file_path': row[4],
                        'changed_features': row[5],
                        'input_features': row[6],
                        'cm_line_pn': row[7],
                        'rev_line_pn': row[8],
                        'failed_line_cm': row[9],
                        'failed_line_rev': row[10],
                        'failed_at_structured': row[11]
                    }
                    comparisons.append(comparison)
                
                return {
                    'status': 'success',
                    'url1': url1,
                    'url2': url2,
                    'doc1_id': doc1,
                    'doc2_id': doc2,
                    'comparison_count': len(comparisons),
                    'comparisons': comparisons
                }
                
        except Exception as e:
            logger.error(f"Error comparing PDFs {url1} and {url2}: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'url1': url1,
                'url2': url2
            }
    
    def _is_valid_url(self, url: str) -> bool:
        """
        Validate URL format.
        
        Args:
            url (str): URL to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            result = urllib.parse.urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def close_connection(self):
        """Close database connection."""
        try:
            if self.conn:
                self.conn.close()
                logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {str(e)}")
    
    def __del__(self):
        """Destructor to ensure connection is closed."""
        self.close_connection()
