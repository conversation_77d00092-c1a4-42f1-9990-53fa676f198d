{% extends "base.html" %}

{% block title %}PDF Comparison Tool - Upload{% endblock %}

{% block content %}
<div class="upload-section">
    <div class="upload-header">
        <h2>
            <i class="fas fa-upload"></i>
            Upload TSV File for PDF Comparison
        </h2>
        <p class="upload-description">
            Upload a tab-separated values (TSV) file containing URL pairs for PDF comparison analysis.
        </p>
    </div>

    <!-- Instructions Panel -->
    <div class="instructions-panel">
        <h3>
            <i class="fas fa-info-circle"></i>
            File Format Instructions
        </h3>
        <div class="instructions-content">
            <div class="format-example">
                <h4>Expected Format:</h4>
                <div class="code-block">
                    <code>
                        URL1&lt;TAB&gt;URL2<br>
                        http://example.com/pdf1.pdf&lt;TAB&gt;http://example.com/pdf2.pdf<br>
                        http://example.com/pdf3.pdf&lt;TAB&gt;http://example.com/pdf4.pdf
                    </code>
                </div>
            </div>
            <div class="requirements">
                <h4>Requirements:</h4>
                <ul>
                    <li><i class="fas fa-check"></i> File must be tab-separated (.tsv or .txt)</li>
                    <li><i class="fas fa-check"></i> Each row must contain exactly 2 URLs</li>
                    <li><i class="fas fa-check"></i> URLs must be separated by a tab character</li>
                    <li><i class="fas fa-check"></i> URLs must be valid HTTP/HTTPS links</li>
                    <li><i class="fas fa-check"></i> Maximum file size: 16MB</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="upload-form-container">
        <form id="uploadForm" class="upload-form" enctype="multipart/form-data">
            <div class="file-input-container">
                <input type="file" id="fileInput" name="file" accept=".tsv,.txt" required>
                <label for="fileInput" class="file-input-label">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <span class="file-input-text">Choose TSV File</span>
                </label>
                <div class="file-info" id="fileInfo" style="display: none;">
                    <i class="fas fa-file-alt"></i>
                    <span id="fileName"></span>
                    <span id="fileSize"></span>
                </div>
            </div>
            
            <button type="submit" class="upload-btn" id="uploadBtn" disabled>
                <i class="fas fa-upload"></i>
                Upload and Process
            </button>
        </form>
    </div>

    <!-- Progress Section -->
    <div class="progress-section" id="progressSection" style="display: none;">
        <div class="progress-header">
            <h3 id="progressTitle">
                <i class="fas fa-spinner fa-spin"></i>
                Processing...
            </h3>
            <p id="progressDescription">Please wait while we process your file.</p>
        </div>
        
        <div class="progress-bar-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <span class="progress-text" id="progressText">0%</span>
        </div>
        
        <div class="progress-details" id="progressDetails">
            <div class="detail-item">
                <span class="detail-label">Status:</span>
                <span class="detail-value" id="statusText">Uploading...</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">File:</span>
                <span class="detail-value" id="processingFileName">-</span>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="results-section" id="resultsSection" style="display: none;">
        <div class="results-header">
            <h3>
                <i class="fas fa-check-circle"></i>
                Processing Complete
            </h3>
        </div>
        
        <div class="results-summary" id="resultsSummary">
            <!-- Results will be populated by JavaScript -->
        </div>
        
        <div class="results-actions">
            <button class="download-btn" id="downloadBtn">
                <i class="fas fa-download"></i>
                Download Results
            </button>
            <button class="reset-btn" id="resetBtn">
                <i class="fas fa-redo"></i>
                Process Another File
            </button>
        </div>
    </div>

    <!-- Error Section -->
    <div class="error-section" id="errorSection" style="display: none;">
        <div class="error-header">
            <h3>
                <i class="fas fa-exclamation-triangle"></i>
                Processing Error
            </h3>
        </div>
        
        <div class="error-message" id="errorMessage">
            <!-- Error message will be populated by JavaScript -->
        </div>
        
        <div class="error-actions">
            <button class="reset-btn" id="errorResetBtn">
                <i class="fas fa-redo"></i>
                Try Again
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/upload.js') }}"></script>
{% endblock %}
