{% extends "base.html" %}

{% block title %}PDF Comparison Tool{% endblock %}

{% block content %}
<div class="upload-container">
    <h2>PDF Comparison Tool</h2>
    <p>Select a TSV file containing URL pairs (separated by tabs)</p>

    <form id="uploadForm" enctype="multipart/form-data">
        <div class="file-section">
            <label for="fileInput">File:</label>
            <input type="file" id="fileInput" name="file" accept=".tsv,.txt" required>
            <button type="submit" id="uploadBtn" disabled>Process File</button>
        </div>
    </form>

    <div id="statusDiv" style="display: none;">
        <p id="statusText">Processing...</p>
    </div>

    <div id="resultsDiv" style="display: none;">
        <p id="resultsText"></p>
        <button id="downloadBtn">Download Results</button>
        <button id="resetBtn">Process Another File</button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/upload.js') }}"></script>
{% endblock %}
